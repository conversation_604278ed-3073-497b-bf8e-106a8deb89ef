# EmailJS Setup Guide for Contact Form

This guide will help you set up EmailJS to make your contact form fully functional.

## 📧 Step 1: Create EmailJS Account

1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Click "Sign Up" and create a free account
3. Verify your email address

## 🔧 Step 2: Add Email Service

1. In your EmailJS dashboard, go to **Email Services**
2. Click **Add New Service**
3. Choose your email provider:
   - **Gmail** (recommended for personal use)
   - **Outlook**
   - **Yahoo**
   - **Custom SMTP**
4. Follow the setup instructions for your chosen provider
5. **Copy the Service ID** (you'll need this later)

## 📝 Step 3: Create Email Template

1. Go to **Email Templates** in your dashboard
2. Click **Create New Template**
3. Use this template content:

### Template Settings:
- **Template Name**: Contact Form
- **Subject**: New message from {{from_name}} - {{subject}}

### Template Content:
```
Hello {{to_name}},

You have received a new message from your portfolio contact form:

From: {{from_name}}
Email: {{from_email}}
Subject: {{subject}}

Message:
{{message}}

---
This message was sent from your portfolio website.
Reply to: {{reply_to}}
```

4. **Copy the Template ID** (you'll need this later)

## 🔑 Step 4: Get Public Key

1. Go to **Account** → **General**
2. Find your **Public Key**
3. **Copy the Public Key** (you'll need this later)

## ⚙️ Step 5: Configure Your Website

1. Open your `index.html` file
2. Find the EmailJS configuration section (around line 1743):

```javascript
const EMAILJS_CONFIG = {
  publicKey: 'YOUR_PUBLIC_KEY', // Replace with your EmailJS public key
  serviceId: 'YOUR_SERVICE_ID', // Replace with your EmailJS service ID
  templateId: 'YOUR_TEMPLATE_ID' // Replace with your EmailJS template ID
};
```

3. Replace the placeholder values:
   - `YOUR_PUBLIC_KEY` → Your Public Key from Step 4
   - `YOUR_SERVICE_ID` → Your Service ID from Step 2
   - `YOUR_TEMPLATE_ID` → Your Template ID from Step 3

### Example:
```javascript
const EMAILJS_CONFIG = {
  publicKey: 'user_abc123def456',
  serviceId: 'service_xyz789',
  templateId: 'template_contact123'
};
```

## 🧪 Step 6: Test Your Form

1. Save your changes
2. Open your website in a browser
3. Fill out the contact form with test data
4. Submit the form
5. Check your email for the test message

## 🔒 Step 7: Security Best Practices

### Rate Limiting (Recommended)
1. In EmailJS dashboard, go to **Account** → **Security**
2. Enable **Rate Limiting** to prevent spam
3. Set reasonable limits (e.g., 50 emails per hour)

### Email Filtering
1. Set up email filters in your email client
2. Create a folder for portfolio messages
3. Filter by subject line containing your name

## 🚨 Troubleshooting

### Common Issues:

**Form shows "Error" message:**
- Check browser console for detailed error messages
- Verify all IDs are correct (Public Key, Service ID, Template ID)
- Ensure your email service is properly connected

**Emails not received:**
- Check spam/junk folder
- Verify template variables match the JavaScript code
- Test with a different email address

**"Invalid email" error:**
- Check email service configuration
- Verify email service is active and connected

### Debug Mode:
Add this to your browser console to see detailed logs:
```javascript
localStorage.setItem('debug', 'emailjs*');
```

## 📊 Step 8: Monitor Usage

1. Check your EmailJS dashboard regularly
2. Monitor email delivery rates
3. Review monthly usage (free plan has limits)
4. Upgrade plan if needed for higher volume

## 🎯 Free Plan Limits

- **200 emails/month**
- **2 email services**
- **3 email templates**
- **50KB attachment size**

For higher volume, consider upgrading to a paid plan.

## 📞 Support

- **EmailJS Documentation**: https://www.emailjs.com/docs/
- **EmailJS Support**: https://www.emailjs.com/support/
- **Community Forum**: Available in EmailJS dashboard

---

## ✅ Quick Checklist

- [ ] EmailJS account created
- [ ] Email service added and configured
- [ ] Email template created with correct variables
- [ ] Public Key, Service ID, and Template ID copied
- [ ] Configuration updated in index.html
- [ ] Form tested successfully
- [ ] Rate limiting enabled
- [ ] Email filters set up

Once completed, your contact form will be fully functional and ready to receive messages from visitors to your portfolio!
