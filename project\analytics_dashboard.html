<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Analytics Dashboard</title>
<script src="https://cdn.tailwindcss.com/3.4.16"></script>
<script>tailwind.config={theme:{extend:{colors:{primary:'#4f46e5',secondary:'#8b5cf6'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
<style>
:where([class^="ri-"])::before { content: "\f3c2"; }
body {
font-family: 'Inter', sans-serif;
background-color: #f9fafb;
}
.widget {
transition: all 0.2s ease;
}
.widget:hover {
box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
input[type="range"] {
-webkit-appearance: none;
height: 6px;
background: #e5e7eb;
border-radius: 5px;
background-image: linear-gradient(#4f46e5, #4f46e5);
background-repeat: no-repeat;
}
input[type="range"]::-webkit-slider-thumb {
-webkit-appearance: none;
height: 16px;
width: 16px;
border-radius: 50%;
background: #4f46e5;
cursor: pointer;
box-shadow: 0 0 2px 0 #555;
}
input[type="range"]::-webkit-slider-runnable-track {
-webkit-appearance: none;
box-shadow: none;
border: none;
background: transparent;
}
.toggle-checkbox:checked {
right: 0;
border-color: #4f46e5;
}
.toggle-checkbox:checked + .toggle-label {
background-color: #4f46e5;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
-webkit-appearance: none;
margin: 0;
}
.custom-checkbox {
display: none;
}
.custom-checkbox + label {
display: inline-block;
position: relative;
padding-left: 30px;
cursor: pointer;
line-height: 20px;
}
.custom-checkbox + label:before {
content: '';
position: absolute;
left: 0;
top: 0;
width: 20px;
height: 20px;
border: 2px solid #d1d5db;
background: #fff;
border-radius: 4px;
}
.custom-checkbox:checked + label:before {
background: #4f46e5;
border-color: #4f46e5;
}
.custom-checkbox:checked + label:after {
content: '';
position: absolute;
left: 7px;
top: 3px;
width: 6px;
height: 12px;
border: solid white;
border-width: 0 2px 2px 0;
transform: rotate(45deg);
}
.grid-stack-item {
border-radius: 8px;
background: white;
}
.date-picker {
position: relative;
}
.date-picker-dropdown {
display: none;
position: absolute;
top: 100%;
left: 0;
z-index: 10;
}
.date-picker.active .date-picker-dropdown {
display: block;
}
</style>
</head>
<body class="min-h-screen">
<!-- Header Section -->
<header class="bg-white shadow-sm">
<div class="flex items-center justify-between px-6 py-3 border-b">
<div class="flex items-center justify-between w-full">
<div class="flex items-center">
<div class="text-2xl font-['Pacifico'] text-primary mr-4 md:mr-8">logo</div>
<nav class="hidden md:flex space-x-6">
<a href="#" class="text-primary font-medium">Dashboard</a>
<a href="#" class="text-gray-600 hover:text-primary">Reports</a>
<a href="#" class="text-gray-600 hover:text-primary">Analytics</a>
<a href="#" class="text-gray-600 hover:text-primary">Settings</a>
</nav>
</div>
<button class="md:hidden w-10 h-10 flex items-center justify-center text-gray-500" id="mobileMenuButton">
<i class="ri-menu-line text-xl"></i>
</button>
</div>
<div class="flex items-center space-x-2 md:space-x-4">
<div class="relative hidden md:block">
<div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
<div class="w-5 h-5 flex items-center justify-center text-gray-400">
<i class="ri-search-line"></i>
</div>
</div>
<input type="text" class="pl-10 pr-4 py-2 w-48 md:w-64 border-none bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary" placeholder="Search dashboards, reports, metrics...">
</div>
<div class="w-9 h-9 flex items-center justify-center bg-gray-100 rounded-full relative cursor-pointer">
<i class="ri-notification-3-line text-gray-600"></i>
<span class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
</div>
<div class="w-9 h-9 flex items-center justify-center bg-gray-100 rounded-full cursor-pointer">
<i class="ri-settings-4-line text-gray-600"></i>
</div>
<div class="flex items-center space-x-2 cursor-pointer">
<div class="w-9 h-9 rounded-full bg-primary flex items-center justify-center text-white font-medium">JD</div>
<div class="hidden md:block">
<div class="text-sm font-medium">John Doe</div>
<div class="text-xs text-gray-500">Admin</div>
</div>
</div>
</div>
</div>
<!-- Filter Bar -->
<div class="px-4 md:px-6 py-3 flex flex-wrap items-center justify-between gap-3 bg-white overflow-x-auto whitespace-nowrap">
<div class="flex items-center space-x-2 md:space-x-3">
<div class="date-picker relative">
<button id="dateRangeButton" class="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-button text-sm font-medium hover:bg-gray-50 whitespace-nowrap">
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-calendar-line"></i>
</div>
<span>May 21 - May 28, 2025</span>
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
<div class="date-picker-dropdown mt-1 bg-white border border-gray-200 rounded shadow-lg p-4 w-72">
<div class="grid grid-cols-2 gap-2 mb-3">
<button class="px-3 py-2 text-sm bg-primary text-white rounded-button whitespace-nowrap">Last 7 days</button>
<button class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-button whitespace-nowrap">Last 30 days</button>
<button class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-button whitespace-nowrap">This month</button>
<button class="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-button whitespace-nowrap">Last month</button>
</div>
<div class="flex space-x-2 mb-3">
<div class="w-1/2">
<label class="block text-xs text-gray-500 mb-1">Start date</label>
<input type="date" class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
</div>
<div class="w-1/2">
<label class="block text-xs text-gray-500 mb-1">End date</label>
<input type="date" class="w-full px-3 py-2 border border-gray-300 rounded text-sm">
</div>
</div>
<div class="flex justify-end">
<button class="px-4 py-2 bg-primary text-white rounded-button text-sm font-medium whitespace-nowrap">Apply</button>
</div>
</div>
</div>
<div class="relative">
<button class="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-button text-sm font-medium hover:bg-gray-50 whitespace-nowrap">
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-filter-3-line"></i>
</div>
<span>All Categories</span>
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
</div>
<div class="relative">
<button class="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-button text-sm font-medium hover:bg-gray-50 whitespace-nowrap">
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-map-pin-line"></i>
</div>
<span>All Regions</span>
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
</div>
<div class="relative">
<button class="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-button text-sm font-medium hover:bg-gray-50 whitespace-nowrap">
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-database-2-line"></i>
</div>
<span>All Sources</span>
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-arrow-down-s-line"></i>
</div>
</button>
</div>
</div>
<div class="flex items-center space-x-3">
<div class="flex items-center space-x-2">
<span class="text-sm text-gray-600">Auto-refresh:</span>
<div class="relative inline-block w-10 mr-2 align-middle select-none">
<input type="checkbox" id="toggleRefresh" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" checked/>
<label for="toggleRefresh" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
</div>
<span id="refreshInterval" class="text-sm font-medium">30s</span>
<div id="refreshDropdown" class="hidden absolute mt-2 py-2 w-24 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
<button class="w-full px-4 py-2 text-sm text-left hover:bg-gray-100" data-value="off">Off</button>
<button class="w-full px-4 py-2 text-sm text-left hover:bg-gray-100" data-value="15s">15s</button>
<button class="w-full px-4 py-2 text-sm text-left hover:bg-gray-100" data-value="30s">30s</button>
<button class="w-full px-4 py-2 text-sm text-left hover:bg-gray-100" data-value="1m">1m</button>
<button class="w-full px-4 py-2 text-sm text-left hover:bg-gray-100" data-value="5m">5m</button>
<button class="w-full px-4 py-2 text-sm text-left hover:bg-gray-100" data-value="15m">15m</button>
<button class="w-full px-4 py-2 text-sm text-left hover:bg-gray-100" data-value="30m">30m</button>
</div>
</div>
<div id="toast" class="hidden fixed bottom-4 right-4 px-4 py-2 bg-gray-800 text-white text-sm rounded-lg shadow-lg z-50"></div>
<button class="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-button text-sm font-medium hover:bg-gray-50 whitespace-nowrap">
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-refresh-line"></i>
</div>
<span>Refresh</span>
</button>
<button class="flex items-center space-x-2 px-3 py-2 bg-primary text-white rounded-button text-sm font-medium hover:bg-primary/90 whitespace-nowrap">
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-add-line"></i>
</div>
<span>Add Widget</span>
</button>
<button class="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-button text-sm font-medium hover:bg-gray-50 whitespace-nowrap">
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-download-2-line"></i>
</div>
<span>Export</span>
</button>
</div>
</div>
</header>
<!-- Dashboard Grid -->
<main class="container mx-auto px-6 py-6">
<div class="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6">
<!-- KPI Cards Row -->
<div class="col-span-1 md:col-span-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
<!-- KPI Card 1 -->
<div class="widget bg-white p-5 rounded shadow">
<div class="flex justify-between items-start mb-4">
<div>
<h3 class="text-sm font-medium text-gray-500">Total Revenue</h3>
<p class="text-2xl font-bold">$1,482,359</p>
</div>
<div class="w-8 h-8 flex items-center justify-center bg-blue-100 rounded-full text-primary">
<i class="ri-money-dollar-circle-line"></i>
</div>
</div>
<div class="flex items-center">
<div class="flex items-center text-green-500 mr-2">
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-arrow-up-line"></i>
</div>
<span class="text-sm font-medium">8.2%</span>
</div>
<span class="text-xs text-gray-500">vs last period</span>
</div>
</div>
<!-- KPI Card 2 -->
<div class="widget bg-white p-5 rounded shadow">
<div class="flex justify-between items-start mb-4">
<div>
<h3 class="text-sm font-medium text-gray-500">Total Users</h3>
<p class="text-2xl font-bold">245,873</p>
</div>
<div class="w-8 h-8 flex items-center justify-center bg-purple-100 rounded-full text-purple-600">
<i class="ri-user-line"></i>
</div>
</div>
<div class="flex items-center">
<div class="flex items-center text-green-500 mr-2">
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-arrow-up-line"></i>
</div>
<span class="text-sm font-medium">12.5%</span>
</div>
<span class="text-xs text-gray-500">vs last period</span>
</div>
</div>
<!-- KPI Card 3 -->
<div class="widget bg-white p-5 rounded shadow">
<div class="flex justify-between items-start mb-4">
<div>
<h3 class="text-sm font-medium text-gray-500">Conversion Rate</h3>
<p class="text-2xl font-bold">3.42%</p>
</div>
<div class="w-8 h-8 flex items-center justify-center bg-green-100 rounded-full text-green-600">
<i class="ri-percent-line"></i>
</div>
</div>
<div class="flex items-center">
<div class="flex items-center text-red-500 mr-2">
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-arrow-down-line"></i>
</div>
<span class="text-sm font-medium">1.8%</span>
</div>
<span class="text-xs text-gray-500">vs last period</span>
</div>
</div>
<!-- KPI Card 4 -->
<div class="widget bg-white p-5 rounded shadow">
<div class="flex justify-between items-start mb-4">
<div>
<h3 class="text-sm font-medium text-gray-500">Avg. Order Value</h3>
<p class="text-2xl font-bold">$87.24</p>
</div>
<div class="w-8 h-8 flex items-center justify-center bg-amber-100 rounded-full text-amber-600">
<i class="ri-shopping-cart-line"></i>
</div>
</div>
<div class="flex items-center">
<div class="flex items-center text-green-500 mr-2">
<div class="w-4 h-4 flex items-center justify-center">
<i class="ri-arrow-up-line"></i>
</div>
<span class="text-sm font-medium">4.3%</span>
</div>
<span class="text-xs text-gray-500">vs last period</span>
</div>
</div>
</div>
<!-- Revenue Chart -->
<div class="col-span-1 md:col-span-12 lg:col-span-8 widget bg-white rounded shadow">
<div class="p-5 border-b">
<div class="flex justify-between items-center">
<h3 class="font-medium">Revenue Trends</h3>
<div class="flex items-center space-x-2">
<button class="flex items-center space-x-1 px-3 py-1 text-xs bg-gray-100 rounded-full whitespace-nowrap">
<span>Daily</span>
</button>
<button class="flex items-center space-x-1 px-3 py-1 text-xs bg-primary text-white rounded-full whitespace-nowrap">
<span>Weekly</span>
</button>
<button class="flex items-center space-x-1 px-3 py-1 text-xs bg-gray-100 rounded-full whitespace-nowrap">
<span>Monthly</span>
</button>
<div class="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded-full cursor-pointer">
<i class="ri-more-2-fill text-gray-500"></i>
</div>
</div>
</div>
</div>
<div class="p-5">
<div id="revenueChart" class="w-full h-80"></div>
</div>
</div>
<!-- Sales by Region -->
<div class="col-span-1 md:col-span-12 lg:col-span-4 widget bg-white rounded shadow">
<div class="p-5 border-b">
<div class="flex justify-between items-center">
<h3 class="font-medium">Sales by Region</h3>
<div class="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded-full cursor-pointer">
<i class="ri-more-2-fill text-gray-500"></i>
</div>
</div>
</div>
<div class="p-5">
<div id="regionChart" class="w-full h-80"></div>
</div>
</div>
<!-- Top Products -->
<div class="col-span-1 md:col-span-12 lg:col-span-6 widget bg-white rounded shadow">
<div class="p-5 border-b">
<div class="flex justify-between items-center">
<h3 class="font-medium">Top Products</h3>
<div class="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded-full cursor-pointer">
<i class="ri-more-2-fill text-gray-500"></i>
</div>
</div>
</div>
<div class="p-5">
<div id="productsChart" class="w-full h-64"></div>
<div class="mt-4 space-y-3">
<div class="flex items-center">
<div class="w-3 h-3 bg-[rgba(87,181,231,1)] rounded-full mr-2"></div>
<span class="text-sm text-gray-600 mr-auto">Premium Subscription</span>
<span class="text-sm font-medium">$245,281</span>
</div>
<div class="flex items-center">
<div class="w-3 h-3 bg-[rgba(141,211,199,1)] rounded-full mr-2"></div>
<span class="text-sm text-gray-600 mr-auto">Basic Subscription</span>
<span class="text-sm font-medium">$187,492</span>
</div>
<div class="flex items-center">
<div class="w-3 h-3 bg-[rgba(251,191,114,1)] rounded-full mr-2"></div>
<span class="text-sm text-gray-600 mr-auto">Add-on Services</span>
<span class="text-sm font-medium">$125,389</span>
</div>
<div class="flex items-center">
<div class="w-3 h-3 bg-[rgba(252,141,98,1)] rounded-full mr-2"></div>
<span class="text-sm text-gray-600 mr-auto">One-time Purchases</span>
<span class="text-sm font-medium">$98,763</span>
</div>
</div>
</div>
</div>
<!-- User Acquisition -->
<div class="col-span-1 md:col-span-12 lg:col-span-6 widget bg-white rounded shadow">
<div class="p-5 border-b">
<div class="flex justify-between items-center">
<h3 class="font-medium">User Acquisition</h3>
<div class="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded-full cursor-pointer">
<i class="ri-more-2-fill text-gray-500"></i>
</div>
</div>
</div>
<div class="p-5">
<div id="acquisitionChart" class="w-full h-64"></div>
<div class="mt-4 space-y-3">
<div class="flex items-center">
<div class="w-3 h-3 bg-[rgba(87,181,231,1)] rounded-full mr-2"></div>
<span class="text-sm text-gray-600 mr-auto">Organic Search</span>
<span class="text-sm font-medium">42.8%</span>
</div>
<div class="flex items-center">
<div class="w-3 h-3 bg-[rgba(141,211,199,1)] rounded-full mr-2"></div>
<span class="text-sm text-gray-600 mr-auto">Direct Traffic</span>
<span class="text-sm font-medium">25.1%</span>
</div>
<div class="flex items-center">
<div class="w-3 h-3 bg-[rgba(251,191,114,1)] rounded-full mr-2"></div>
<span class="text-sm text-gray-600 mr-auto">Social Media</span>
<span class="text-sm font-medium">18.3%</span>
</div>
<div class="flex items-center">
<div class="w-3 h-3 bg-[rgba(252,141,98,1)] rounded-full mr-2"></div>
<span class="text-sm text-gray-600 mr-auto">Referrals</span>
<span class="text-sm font-medium">13.8%</span>
</div>
</div>
</div>
</div>
<!-- Recent Transactions -->
<div class="col-span-1 md:col-span-12 lg:col-span-8 widget bg-white rounded shadow overflow-hidden">
<div class="p-5 border-b">
<div class="flex justify-between items-center">
<h3 class="font-medium">Recent Transactions</h3>
<button class="text-sm text-primary font-medium whitespace-nowrap">View All</button>
</div>
</div>
<div class="overflow-x-auto">
<table class="min-w-full divide-y divide-gray-200">
<thead class="bg-gray-50">
<tr>
<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction ID</th>
<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
<th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
</tr>
</thead>
<tbody class="bg-white divide-y divide-gray-200">
<tr>
<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">#TRX-8729</td>
<td class="px-6 py-4 whitespace-nowrap">
<div class="flex items-center">
<div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium">EB</div>
<div class="ml-3">
<div class="text-sm font-medium text-gray-900">Emma Brown</div>
<div class="text-xs text-gray-500"><EMAIL></div>
</div>
</div>
</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">May 28, 2025</td>
<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">$1,249.00</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
</td>
</tr>
<tr>
<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">#TRX-8728</td>
<td class="px-6 py-4 whitespace-nowrap">
<div class="flex items-center">
<div class="h-8 w-8 rounded-full bg-amber-100 flex items-center justify-center text-amber-600 font-medium">RJ</div>
<div class="ml-3">
<div class="text-sm font-medium text-gray-900">Robert Johnson</div>
<div class="text-xs text-gray-500"><EMAIL></div>
</div>
</div>
</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">May 27, 2025</td>
<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">$879.50</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
</td>
</tr>
<tr>
<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">#TRX-8727</td>
<td class="px-6 py-4 whitespace-nowrap">
<div class="flex items-center">
<div class="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 font-medium">SL</div>
<div class="ml-3">
<div class="text-sm font-medium text-gray-900">Sarah Liu</div>
<div class="text-xs text-gray-500"><EMAIL></div>
</div>
</div>
</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">May 27, 2025</td>
<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">$1,895.00</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
</td>
</tr>
<tr>
<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">#TRX-8726</td>
<td class="px-6 py-4 whitespace-nowrap">
<div class="flex items-center">
<div class="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center text-green-600 font-medium">MP</div>
<div class="ml-3">
<div class="text-sm font-medium text-gray-900">Michael Patel</div>
<div class="text-xs text-gray-500"><EMAIL></div>
</div>
</div>
</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">May 26, 2025</td>
<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">$349.99</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Failed</span>
</td>
</tr>
<tr>
<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">#TRX-8725</td>
<td class="px-6 py-4 whitespace-nowrap">
<div class="flex items-center">
<div class="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center text-red-600 font-medium">AK</div>
<div class="ml-3">
<div class="text-sm font-medium text-gray-900">Aisha Khan</div>
<div class="text-xs text-gray-500"><EMAIL></div>
</div>
</div>
</td>
<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">May 26, 2025</td>
<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">$2,499.00</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
</td>
</tr>
</tbody>
</table>
</div>
</div>
<!-- Customer Map -->
<div class="col-span-12 lg:col-span-4 widget bg-white rounded shadow">
<div class="p-5 border-b">
<div class="flex justify-between items-center">
<h3 class="font-medium">Customer Distribution</h3>
<div class="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded-full cursor-pointer">
<i class="ri-more-2-fill text-gray-500"></i>
</div>
</div>
</div>
<div class="p-5 h-[400px] relative overflow-hidden">
<div class="absolute inset-0 bg-cover bg-center" style="background-image: url('https://public.readdy.ai/gen_page/map_placeholder_1280x720.png');"></div>
<div class="absolute top-4 left-4 bg-white/90 p-3 rounded shadow-md">
<h4 class="text-xs font-medium text-gray-500 mb-1">Top Regions</h4>
<div class="space-y-2">
<div class="flex items-center">
<div class="w-2 h-2 bg-primary rounded-full mr-2"></div>
<span class="text-xs text-gray-700">North America</span>
<span class="text-xs font-medium ml-auto">42%</span>
</div>
<div class="flex items-center">
<div class="w-2 h-2 bg-blue-400 rounded-full mr-2"></div>
<span class="text-xs text-gray-700">Europe</span>
<span class="text-xs font-medium ml-auto">28%</span>
</div>
<div class="flex items-center">
<div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
<span class="text-xs text-gray-700">Asia Pacific</span>
<span class="text-xs font-medium ml-auto">21%</span>
</div>
<div class="flex items-center">
<div class="w-2 h-2 bg-yellow-400 rounded-full mr-2"></div>
<span class="text-xs text-gray-700">Other</span>
<span class="text-xs font-medium ml-auto">9%</span>
</div>
</div>
</div>
</div>
</div>
</div>
</main>
<!-- Footer -->
<footer class="bg-white border-t mt-6">
<div class="container mx-auto px-6 py-4">
<div class="flex flex-col md:flex-row justify-between items-center">
<div class="flex items-center space-x-4 mb-4 md:mb-0">
<div class="text-sm text-gray-500">
<span class="font-medium">Last updated:</span> May 28, 2025 14:32
</div>
<div class="flex items-center text-sm text-gray-500">
<div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
All systems operational
</div>
</div>
<div class="flex items-center space-x-4">
<button class="text-sm text-gray-600 hover:text-primary whitespace-nowrap">Help Center</button>
<button class="text-sm text-gray-600 hover:text-primary whitespace-nowrap">API Documentation</button>
<button class="text-sm text-gray-600 hover:text-primary whitespace-nowrap">Contact Support</button>
</div>
</div>
</div>
</footer>
<!-- Widget Customization Panel (Hidden by default) -->
<div id="widgetCustomizationPanel" class="fixed inset-y-0 right-0 w-80 bg-white shadow-lg transform translate-x-full transition-transform duration-300 ease-in-out z-50">
<div class="h-full flex flex-col">
<div class="p-4 border-b flex justify-between items-center">
<h3 class="font-medium">Widget Settings</h3>
<button id="closeWidgetPanel" class="w-8 h-8 flex items-center justify-center hover:bg-gray-100 rounded-full">
<i class="ri-close-line"></i>
</button>
</div>
<div class="flex-1 overflow-y-auto p-4">
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Widget Title</label>
<input type="text" class="w-full px-3 py-2 border border-gray-300 rounded text-sm" value="Revenue Trends">
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Data Source</label>
<div class="relative">
<select class="w-full px-3 py-2 border border-gray-300 rounded text-sm appearance-none pr-8">
<option>Google Analytics</option>
<option>Salesforce</option>
<option>Custom API</option>
<option>Database</option>
</select>
<div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
<i class="ri-arrow-down-s-line"></i>
</div>
</div>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Chart Type</label>
<div class="grid grid-cols-3 gap-2">
<button class="p-2 border border-primary bg-primary/10 rounded flex flex-col items-center justify-center whitespace-nowrap">
<div class="w-6 h-6 flex items-center justify-center mb-1">
<i class="ri-line-chart-line text-primary"></i>
</div>
<span class="text-xs">Line</span>
</button>
<button class="p-2 border border-gray-200 rounded flex flex-col items-center justify-center whitespace-nowrap">
<div class="w-6 h-6 flex items-center justify-center mb-1">
<i class="ri-bar-chart-2-line text-gray-500"></i>
</div>
<span class="text-xs">Bar</span>
</button>
<button class="p-2 border border-gray-200 rounded flex flex-col items-center justify-center whitespace-nowrap">
<div class="w-6 h-6 flex items-center justify-center mb-1">
<i class="ri-pie-chart-line text-gray-500"></i>
</div>
<span class="text-xs">Pie</span>
</button>
<button class="p-2 border border-gray-200 rounded flex flex-col items-center justify-center whitespace-nowrap">
<div class="w-6 h-6 flex items-center justify-center mb-1">
<i class="ri-donut-chart-line text-gray-500"></i>
</div>
<span class="text-xs">Donut</span>
</button>
<button class="p-2 border border-gray-200 rounded flex flex-col items-center justify-center whitespace-nowrap">
<div class="w-6 h-6 flex items-center justify-center mb-1">
<i class="ri-table-line text-gray-500"></i>
</div>
<span class="text-xs">Table</span>
</button>
<button class="p-2 border border-gray-200 rounded flex flex-col items-center justify-center whitespace-nowrap">
<div class="w-6 h-6 flex items-center justify-center mb-1">
<i class="ri-numbers-line text-gray-500"></i>
</div>
<span class="text-xs">KPI</span>
</button>
</div>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Dimensions</label>
<div class="space-y-2">
<div class="flex items-center">
<input type="checkbox" id="dim1" class="custom-checkbox">
<label for="dim1" class="text-sm">Date</label>
</div>
<div class="flex items-center">
<input type="checkbox" id="dim2" class="custom-checkbox" checked>
<label for="dim2" class="text-sm">Region</label>
</div>
<div class="flex items-center">
<input type="checkbox" id="dim3" class="custom-checkbox" checked>
<label for="dim3" class="text-sm">Product Category</label>
</div>
<div class="flex items-center">
<input type="checkbox" id="dim4" class="custom-checkbox">
<label for="dim4" class="text-sm">Customer Segment</label>
</div>
</div>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Metrics</label>
<div class="space-y-2">
<div class="flex items-center">
<input type="checkbox" id="met1" class="custom-checkbox" checked>
<label for="met1" class="text-sm">Revenue</label>
</div>
<div class="flex items-center">
<input type="checkbox" id="met2" class="custom-checkbox" checked>
<label for="met2" class="text-sm">Orders</label>
</div>
<div class="flex items-center">
<input type="checkbox" id="met3" class="custom-checkbox">
<label for="met3" class="text-sm">Average Order Value</label>
</div>
<div class="flex items-center">
<input type="checkbox" id="met4" class="custom-checkbox">
<label for="met4" class="text-sm">Conversion Rate</label>
</div>
</div>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Refresh Rate</label>
<div class="flex items-center">
<input type="range" min="0" max="4" value="2" class="w-full" id="refreshRateSlider">
</div>
<div class="flex justify-between text-xs text-gray-500 mt-1">
<span>Off</span>
<span>1m</span>
<span>5m</span>
<span>15m</span>
<span>30m</span>
</div>
</div>
<div class="mb-6">
<label class="block text-sm font-medium text-gray-700 mb-2">Visual Options</label>
<div class="space-y-3">
<div class="flex items-center justify-between">
<span class="text-sm">Show Legend</span>
<div class="relative inline-block w-10 align-middle select-none">
<input type="checkbox" id="toggleLegend" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" checked/>
<label for="toggleLegend" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
</div>
</div>
<div class="flex items-center justify-between">
<span class="text-sm">Show Grid Lines</span>
<div class="relative inline-block w-10 align-middle select-none">
<input type="checkbox" id="toggleGrid" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" checked/>
<label for="toggleGrid" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
</div>
</div>
<div class="flex items-center justify-between">
<span class="text-sm">Smooth Lines</span>
<div class="relative inline-block w-10 align-middle select-none">
<input type="checkbox" id="toggleSmooth" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" checked/>
<label for="toggleSmooth" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
</div>
</div>
<div class="flex items-center justify-between">
<span class="text-sm">Show Data Labels</span>
<div class="relative inline-block w-10 align-middle select-none">
<input type="checkbox" id="toggleLabels" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
<label for="toggleLabels" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
</div>
</div>
</div>
</div>
</div>
<div class="p-4 border-t">
<div class="flex space-x-3">
<button class="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-button text-sm font-medium hover:bg-gray-200 whitespace-nowrap">Cancel</button>
<button class="flex-1 px-4 py-2 bg-primary text-white rounded-button text-sm font-medium hover:bg-primary/90 whitespace-nowrap">Apply</button>
</div>
</div>
</div>
</div>
<script id="datePickerScript">
document.addEventListener('DOMContentLoaded', function() {
const dateRangeButton = document.getElementById('dateRangeButton');
const datePicker = dateRangeButton.closest('.date-picker');
dateRangeButton.addEventListener('click', function() {
datePicker.classList.toggle('active');
});
// Close date picker when clicking outside
document.addEventListener('click', function(event) {
if (!datePicker.contains(event.target)) {
datePicker.classList.remove('active');
}
});
});
</script>
<script id="widgetPanelScript">
document.addEventListener('DOMContentLoaded', function() {
const widgetPanel = document.getElementById('widgetCustomizationPanel');
const closeWidgetPanel = document.getElementById('closeWidgetPanel');
// Auto-refresh functionality
const toggleRefresh = document.getElementById('toggleRefresh');
const refreshDropdown = document.getElementById('refreshDropdown');
const refreshInterval = document.getElementById('refreshInterval');
const toast = document.getElementById('toast');
let refreshTimer = null;
function showToast(message) {
toast.textContent = message;
toast.classList.remove('hidden');
setTimeout(() => {
toast.classList.add('hidden');
}, 3000);
}
function startRefreshTimer(interval) {
if (refreshTimer) clearInterval(refreshTimer);
if (interval === 'off') return;
const timeInMs = {
'15s': 15000,
'30s': 30000,
'1m': 60000,
'5m': 300000,
'15m': 900000,
'30m': 1800000
}[interval];
refreshTimer = setInterval(() => {
// Refresh logic here
console.log('Refreshing data...');
}, timeInMs);
}
toggleRefresh.addEventListener('change', function() {
if (this.checked) {
refreshDropdown.classList.remove('hidden');
const currentInterval = refreshInterval.textContent;
startRefreshTimer(currentInterval);
showToast(`Auto-refresh set to ${currentInterval}`);
} else {
refreshDropdown.classList.add('hidden');
if (refreshTimer) clearInterval(refreshTimer);
showToast('Auto-refresh turned off');
}
});
document.addEventListener('click', function(event) {
if (!event.target.closest('#refreshDropdown') && !event.target.closest('#toggleRefresh')) {
refreshDropdown.classList.add('hidden');
}
});
refreshDropdown.querySelectorAll('button').forEach(button => {
button.addEventListener('click', function() {
const value = this.dataset.value;
refreshInterval.textContent = value;
refreshDropdown.classList.add('hidden');
if (value === 'off') {
toggleRefresh.checked = false;
if (refreshTimer) clearInterval(refreshTimer);
showToast('Auto-refresh turned off');
} else {
toggleRefresh.checked = true;
startRefreshTimer(value);
showToast(`Auto-refresh set to ${value}`);
}
});
});
// Function to open widget panel
window.openWidgetPanel = function() {
widgetPanel.classList.remove('translate-x-full');
};
// Close widget panel
closeWidgetPanel.addEventListener('click', function() {
widgetPanel.classList.add('translate-x-full');
});
// Setup widget more buttons to open panel
const widgetMoreButtons = document.querySelectorAll('.widget .ri-more-2-fill');
widgetMoreButtons.forEach(button => {
button.addEventListener('click', window.openWidgetPanel);
});
// Setup range slider
const refreshRateSlider = document.getElementById('refreshRateSlider');
if (refreshRateSlider) {
refreshRateSlider.addEventListener('input', function() {
const value = (this.value - this.min) / (this.max - this.min) * 100;
this.style.backgroundImage = `linear-gradient(to right, #4f46e5 ${value}%, #e5e7eb ${value}%)`;
});
// Initialize slider background
const initialValue = (refreshRateSlider.value - refreshRateSlider.min) / (refreshRateSlider.max - refreshRateSlider.min) * 100;
refreshRateSlider.style.backgroundImage = `linear-gradient(to right, #4f46e5 ${initialValue}%, #e5e7eb ${initialValue}%)`;
}
});
</script>
<script id="mobileMenuScript">
document.addEventListener('DOMContentLoaded', function() {
const mobileMenuButton = document.getElementById('mobileMenuButton');
const header = document.querySelector('header');
mobileMenuButton.addEventListener('click', function() {
const nav = document.createElement('div');
nav.className = 'md:hidden fixed inset-0 bg-white z-50';
nav.innerHTML = `
<div class="flex flex-col h-full">
<div class="flex items-center justify-between p-4 border-b">
<div class="text-2xl font-['Pacifico'] text-primary">logo</div>
<button class="w-10 h-10 flex items-center justify-center text-gray-500" id="closeMobileMenu">
<i class="ri-close-line text-xl"></i>
</button>
</div>
<div class="p-4">
<div class="relative mb-4">
<div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
<div class="w-5 h-5 flex items-center justify-center text-gray-400">
<i class="ri-search-line"></i>
</div>
</div>
<input type="text" class="w-full pl-10 pr-4 py-2 border-none bg-gray-100 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary" placeholder="Search...">
</div>
<nav class="flex flex-col space-y-4">
<a href="#" class="text-primary font-medium text-lg">Dashboard</a>
<a href="#" class="text-gray-600 hover:text-primary text-lg">Reports</a>
<a href="#" class="text-gray-600 hover:text-primary text-lg">Analytics</a>
<a href="#" class="text-gray-600 hover:text-primary text-lg">Settings</a>
</nav>
</div>
</div>
`;
document.body.appendChild(nav);
const closeMobileMenu = document.getElementById('closeMobileMenu');
closeMobileMenu.addEventListener('click', function() {
nav.remove();
});
});
});
</script>
<script id="chartsScript">
document.addEventListener('DOMContentLoaded', function() {
// Revenue Chart
const revenueChart = echarts.init(document.getElementById('revenueChart'));
const revenueOption = {
animation: false,
tooltip: {
trigger: 'axis',
backgroundColor: 'rgba(255, 255, 255, 0.8)',
borderColor: '#e2e8f0',
borderWidth: 1,
textStyle: {
color: '#1f2937'
}
},
grid: {
left: '3%',
right: '4%',
bottom: '3%',
top: '3%',
containLabel: true
},
xAxis: {
type: 'category',
boundaryGap: false,
data: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7', 'Week 8'],
axisLine: {
lineStyle: {
color: '#e2e8f0'
}
},
axisLabel: {
color: '#1f2937'
}
},
yAxis: {
type: 'value',
axisLine: {
show: false
},
axisLabel: {
color: '#1f2937'
},
splitLine: {
lineStyle: {
color: '#e2e8f0'
}
}
},
series: [
{
name: 'Revenue',
type: 'line',
smooth: true,
symbol: 'none',
lineStyle: {
width: 3,
color: 'rgba(87, 181, 231, 1)'
},
areaStyle: {
color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
{
offset: 0,
color: 'rgba(87, 181, 231, 0.3)'
},
{
offset: 1,
color: 'rgba(87, 181, 231, 0.05)'
}
])
},
data: [150000, 180000, 220000, 270000, 320000, 250000, 290000, 350000]
},
{
name: 'Orders',
type: 'line',
smooth: true,
symbol: 'none',
lineStyle: {
width: 3,
color: 'rgba(141, 211, 199, 1)'
},
areaStyle: {
color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
{
offset: 0,
color: 'rgba(141, 211, 199, 0.3)'
},
{
offset: 1,
color: 'rgba(141, 211, 199, 0.05)'
}
])
},
data: [1200, 1400, 1800, 2200, 2600, 2100, 2400, 2900]
}
]
};
revenueChart.setOption(revenueOption);
// Region Chart
const regionChart = echarts.init(document.getElementById('regionChart'));
const regionOption = {
animation: false,
tooltip: {
trigger: 'item',
backgroundColor: 'rgba(255, 255, 255, 0.8)',
borderColor: '#e2e8f0',
borderWidth: 1,
textStyle: {
color: '#1f2937'
}
},
series: [
{
name: 'Sales by Region',
type: 'pie',
radius: ['40%', '70%'],
center: ['50%', '50%'],
avoidLabelOverlap: false,
itemStyle: {
borderRadius: 8,
borderColor: '#fff',
borderWidth: 2
},
label: {
show: false
},
emphasis: {
label: {
show: true,
fontSize: '14',
fontWeight: 'bold'
}
},
labelLine: {
show: false
},
data: [
{ value: 42, name: 'North America', itemStyle: { color: 'rgba(87, 181, 231, 1)' } },
{ value: 28, name: 'Europe', itemStyle: { color: 'rgba(141, 211, 199, 1)' } },
{ value: 21, name: 'Asia Pacific', itemStyle: { color: 'rgba(251, 191, 114, 1)' } },
{ value: 9, name: 'Other', itemStyle: { color: 'rgba(252, 141, 98, 1)' } }
]
}
]
};
regionChart.setOption(regionOption);
// Products Chart
const productsChart = echarts.init(document.getElementById('productsChart'));
const productsOption = {
animation: false,
tooltip: {
trigger: 'item',
backgroundColor: 'rgba(255, 255, 255, 0.8)',
borderColor: '#e2e8f0',
borderWidth: 1,
textStyle: {
color: '#1f2937'
}
},
series: [
{
name: 'Top Products',
type: 'pie',
radius: '70%',
center: ['50%', '50%'],
data: [
{ value: 245281, name: 'Premium Subscription', itemStyle: { color: 'rgba(87, 181, 231, 1)' } },
{ value: 187492, name: 'Basic Subscription', itemStyle: { color: 'rgba(141, 211, 199, 1)' } },
{ value: 125389, name: 'Add-on Services', itemStyle: { color: 'rgba(251, 191, 114, 1)' } },
{ value: 98763, name: 'One-time Purchases', itemStyle: { color: 'rgba(252, 141, 98, 1)' } }
],
emphasis: {
itemStyle: {
shadowBlur: 10,
shadowOffsetX: 0,
shadowColor: 'rgba(0, 0, 0, 0.5)'
}
},
label: {
show: false
}
}
]
};
productsChart.setOption(productsOption);
// Acquisition Chart
const acquisitionChart = echarts.init(document.getElementById('acquisitionChart'));
const acquisitionOption = {
animation: false,
tooltip: {
trigger: 'item',
backgroundColor: 'rgba(255, 255, 255, 0.8)',
borderColor: '#e2e8f0',
borderWidth: 1,
textStyle: {
color: '#1f2937'
}
},
series: [
{
name: 'User Acquisition',
type: 'pie',
radius: '70%',
center: ['50%', '50%'],
data: [
{ value: 42.8, name: 'Organic Search', itemStyle: { color: 'rgba(87, 181, 231, 1)' } },
{ value: 25.1, name: 'Direct Traffic', itemStyle: { color: 'rgba(141, 211, 199, 1)' } },
{ value: 18.3, name: 'Social Media', itemStyle: { color: 'rgba(251, 191, 114, 1)' } },
{ value: 13.8, name: 'Referrals', itemStyle: { color: 'rgba(252, 141, 98, 1)' } }
],
emphasis: {
itemStyle: {
shadowBlur: 10,
shadowOffsetX: 0,
shadowColor: 'rgba(0, 0, 0, 0.5)'
}
},
label: {
show: false
}
}
]
};
acquisitionChart.setOption(acquisitionOption);
// Handle window resize
window.addEventListener('resize', function() {
revenueChart.resize();
regionChart.resize();
productsChart.resize();
acquisitionChart.resize();
});
});
</script>
</body>
</html>