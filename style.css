 :where([class^="ri-"])::before { content: "\f3c2"; }
      body {
      font-family: 'Inter', sans-serif;
      }
      .scroll-down-arrow {
      animation: bounce 2s infinite;
      }
      @keyframes bounce {
      0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
      40% { transform: translateY(-10px); }
      60% { transform: translateY(-5px); }
      }
      .scroll-top-button {
      opacity: 0;
      visibility: hidden;
      transition: opacity 0.3s, visibility 0.3s;
      }
      .scroll-top-button.visible {
      opacity: 1;
      visibility: visible;
      }
      input:focus, button:focus {
      outline: none;
      }
      .animate-fade-up {
      opacity: 0;
      transform: translateY(30px);
      transition: opacity 0.6s ease-out, transform 0.6s ease-out;
      }
      .animate-fade-up.visible {
      opacity: 1;
      transform: translateY(0);
      }
      .animate-fade-in {
      opacity: 0;
      transition: opacity 0.6s ease-out;
      }
      .animate-fade-in.visible {
      opacity: 1;
      }
      .stagger-animation {
      transition-delay: calc(var(--animation-order) * 0.1s);
      }
      .scale-hover {
      transition: transform 0.3s ease;
      }
      .scale-hover:hover {
      transform: scale(1.02);
      }
      /* .nav-link {
      position: relative;
      }
      .nav-link::after {
      content: '';
      position: absolute;
      width: 0;
      height: 2px;
      bottom: -4px;
      left: 0;
      background-color: #15B8A6;
      transition: width 0.3s ease;
      }
      .nav-link:hover::after {
      width: 100%;
      } */
      .progress-bar {
        position: fixed;
        top: 0;
        left: 0;
        height: 3px;
        background: #15B8A6;
        z-index: 9999;
        transition: width 0.1s ease;
      }
      @keyframes blink-caret {
      from, to { border-color: transparent }
      50% { border-color: #15B8A6 }
      }

      /* Enhanced Background Animations */
      @keyframes fall {
        0% {
          transform: translateY(-100vh) rotate(0deg);
          opacity: 0;
        }
        10% {
          opacity: 1;
        }
        90% {
          opacity: 1;
        }
        100% {
          transform: translateY(100vh) rotate(360deg);
          opacity: 0;
        }
      }

      @keyframes slideInLeft {
        0% { transform: translateX(-100px); opacity: 0; }
        100% { transform: translateX(0); opacity: 1; }
      }

      /* Background Elements */
      .bg-animated {
        position: relative;
        overflow: hidden;
      }

      .bg-animated::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
          rgba(21, 184, 166, 0.1) 0%,
          rgba(16, 185, 129, 0.05) 25%,
          rgba(59, 130, 246, 0.05) 50%,
          rgba(139, 92, 246, 0.1) 75%,
          rgba(236, 72, 153, 0.05) 100%);
        z-index: 1;
      }

      /* Honeycomb Elements */
      .honeycomb {
        position: absolute;
        width: 40px;
        height: 40px;
        background: linear-gradient(45deg, rgba(21, 184, 166, 0.15), rgba(16, 185, 129, 0.1));
        backdrop-filter: blur(8px);
        z-index: 2;
        clip-path: polygon(30% 0%, 70% 0%, 100% 50%, 70% 100%, 30% 100%, 0% 50%);
        animation: fall linear infinite;
      }

      .honeycomb:nth-child(1) {
        left: 10%;
        animation-duration: 8s;
        animation-delay: 0s;
      }

      .honeycomb:nth-child(2) {
        left: 20%;
        animation-duration: 10s;
        animation-delay: -2s;
        background: linear-gradient(45deg, rgba(59, 130, 246, 0.12), rgba(139, 92, 246, 0.08));
      }

      .honeycomb:nth-child(3) {
        left: 30%;
        animation-duration: 12s;
        animation-delay: -4s;
        width: 35px;
        height: 35px;
      }

      .honeycomb:nth-child(4) {
        left: 40%;
        animation-duration: 9s;
        animation-delay: -1s;
        background: linear-gradient(45deg, rgba(236, 72, 153, 0.1), rgba(251, 146, 60, 0.08));
      }

      .honeycomb:nth-child(5) {
        left: 50%;
        animation-duration: 11s;
        animation-delay: -3s;
        width: 45px;
        height: 45px;
      }

      .honeycomb:nth-child(6) {
        left: 60%;
        animation-duration: 7s;
        animation-delay: -5s;
        background: linear-gradient(45deg, rgba(16, 185, 129, 0.12), rgba(21, 184, 166, 0.09));
      }

      .honeycomb:nth-child(7) {
        left: 70%;
        animation-duration: 13s;
        animation-delay: -2.5s;
        width: 38px;
        height: 38px;
      }

      .honeycomb:nth-child(8) {
        left: 80%;
        animation-duration: 8.5s;
        animation-delay: -4.5s;
        background: linear-gradient(45deg, rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.08));
      }

      .honeycomb:nth-child(9) {
        left: 90%;
        animation-duration: 10.5s;
        animation-delay: -1.5s;
        width: 42px;
        height: 42px;
      }

      .honeycomb:nth-child(10) {
        left: 15%;
        animation-duration: 9.5s;
        animation-delay: -3.5s;
        width: 36px;
        height: 36px;
        background: linear-gradient(45deg, rgba(251, 146, 60, 0.12), rgba(236, 72, 153, 0.1));
      }

      .honeycomb:nth-child(11) {
        left: 25%;
        animation-duration: 11.5s;
        animation-delay: -0.5s;
        background: linear-gradient(45deg, rgba(139, 92, 246, 0.15), rgba(59, 130, 246, 0.1));
      }

      .honeycomb:nth-child(12) {
        left: 35%;
        animation-duration: 8.2s;
        animation-delay: -2.8s;
        width: 44px;
        height: 44px;
      }

      .honeycomb:nth-child(13) {
        left: 45%;
        animation-duration: 12.5s;
        animation-delay: -4.2s;
        background: linear-gradient(45deg, rgba(16, 185, 129, 0.1), rgba(21, 184, 166, 0.12));
        width: 38px;
        height: 38px;
      }

      .honeycomb:nth-child(14) {
        left: 55%;
        animation-duration: 7.8s;
        animation-delay: -1.8s;
        background: linear-gradient(45deg, rgba(236, 72, 153, 0.12), rgba(251, 146, 60, 0.09));
      }

      .honeycomb:nth-child(15) {
        left: 65%;
        animation-duration: 10.8s;
        animation-delay: -3.2s;
        width: 41px;
        height: 41px;
      }

      .honeycomb:nth-child(16) {
        left: 75%;
        animation-duration: 9.2s;
        animation-delay: -5.2s;
        background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.12));
        width: 37px;
        height: 37px;
      }

      .honeycomb:nth-child(17) {
        left: 85%;
        animation-duration: 11.8s;
        animation-delay: -0.8s;
        background: linear-gradient(45deg, rgba(21, 184, 166, 0.12), rgba(16, 185, 129, 0.1));
      }

      .honeycomb:nth-child(18) {
        left: 5%;
        animation-duration: 8.8s;
        animation-delay: -4.8s;
        width: 43px;
        height: 43px;
        background: linear-gradient(45deg, rgba(251, 146, 60, 0.1), rgba(236, 72, 153, 0.08));
      }

      .honeycomb:nth-child(19) {
        left: 95%;
        animation-duration: 13.2s;
        animation-delay: -2.2s;
        width: 39px;
        height: 39px;
      }

      .honeycomb:nth-child(20) {
        left: 12%;
        animation-duration: 7.5s;
        animation-delay: -6s;
        background: linear-gradient(45deg, rgba(139, 92, 246, 0.08), rgba(59, 130, 246, 0.12));
        width: 46px;
        height: 46px;
      }

      /* Glassmorphism Effect */
      .glass-effect {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      /* Enhanced Section Backgrounds */
      .section-pattern {
        background-image:
          radial-gradient(circle at 25% 25%, rgba(21, 184, 166, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
      }

      /* Simple Profile Image Styles */
      .simple-profile-container {
        width: 320px;
        height: 320px;
        margin: 0 auto;
        position: relative;
        transition: transform 0.3s ease;
      }

      .simple-profile-container:hover {
        transform: translateY(-5px);
      }

      /* Background Elements */
      .bg-element {
        position: absolute;
        border-radius: 50%;
        opacity: 0.6;
        transition: all 0.3s ease;
      }

      .bg-element-1 {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, rgba(21, 184, 166, 0.2), rgba(16, 185, 129, 0.3));
        top: -20px;
        left: -20px;
        animation: float 6s ease-in-out infinite;
      }

      .bg-element-2 {
        width: 80px;
        height: 80px;
        background: linear-gradient(45deg, rgba(6, 182, 212, 0.25), rgba(139, 92, 246, 0.2));
        bottom: -10px;
        right: -10px;
        animation: float 4s ease-in-out infinite reverse;
      }

      .bg-element-3 {
        width: 60px;
        height: 60px;
        background: linear-gradient(225deg, rgba(236, 72, 153, 0.2), rgba(251, 146, 60, 0.25));
        top: 50%;
        left: -30px;
        transform: translateY(-50%);
        animation: float 5s ease-in-out infinite;
      }

      /* Profile Image Wrapper */
      .profile-wrapper {
        position: relative;
        z-index: 10;
        width: 100%;
        height: 100%;
        border-radius: 20px;
        overflow: hidden;
        box-shadow:
          0 20px 40px rgba(0, 0, 0, 0.1),
          0 10px 20px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
      }

      .simple-profile-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center top;
        transition: transform 0.3s ease;
      }

      /* Hover Effects */
      .simple-profile-container:hover .profile-wrapper {
        box-shadow:
          0 30px 60px rgba(21, 184, 166, 0.15),
          0 15px 30px rgba(21, 184, 166, 0.1);
        transform: scale(1.02);
      }

      .simple-profile-container:hover .simple-profile-image {
        transform: scale(1.05);
      }

      .simple-profile-container:hover .bg-element-1 {
        opacity: 0.8;
        transform: scale(1.1);
      }

      .simple-profile-container:hover .bg-element-2 {
        opacity: 0.8;
        transform: scale(1.15);
      }

      .simple-profile-container:hover .bg-element-3 {
        opacity: 0.8;
        transform: translateY(-50%) scale(1.2);
      }

      /* Floating Animation */
      @keyframes float {
        0%, 100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
      }

      /* Curved Background Shapes */
      .curve-shape {
        position: absolute;
        border-radius: 50% 30% 70% 40%;
        background: linear-gradient(45deg, rgba(21, 184, 166, 0.1), rgba(16, 185, 129, 0.15));
        backdrop-filter: blur(20px);
        animation: morphing 8s ease-in-out infinite;
      }

      .curve-shape-2 {
        position: absolute;
        border-radius: 40% 60% 30% 70%;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(139, 92, 246, 0.12));
        backdrop-filter: blur(15px);
        animation: morphing-reverse 10s ease-in-out infinite;
      }

      .curve-shape-3 {
        position: absolute;
        border-radius: 60% 40% 50% 30%;
        background: linear-gradient(225deg, rgba(236, 72, 153, 0.06), rgba(251, 146, 60, 0.1));
        backdrop-filter: blur(25px);
        animation: morphing-slow 12s ease-in-out infinite;
      }

      @keyframes morphing {
        0%, 100% {
          border-radius: 50% 30% 70% 40%;
          transform: translateY(0px) rotate(0deg);
        }
        25% {
          border-radius: 30% 70% 40% 50%;
          transform: translateY(-15px) rotate(90deg);
        }
        50% {
          border-radius: 70% 40% 50% 30%;
          transform: translateY(-30px) rotate(180deg);
        }
        75% {
          border-radius: 40% 50% 30% 70%;
          transform: translateY(-15px) rotate(270deg);
        }
      }

      @keyframes morphing-reverse {
        0%, 100% {
          border-radius: 40% 60% 30% 70%;
          transform: translateY(0px) rotate(0deg);
        }
        25% {
          border-radius: 60% 30% 70% 40%;
          transform: translateY(20px) rotate(-90deg);
        }
        50% {
          border-radius: 30% 70% 40% 60%;
          transform: translateY(40px) rotate(-180deg);
        }
        75% {
          border-radius: 70% 40% 60% 30%;
          transform: translateY(20px) rotate(-270deg);
        }
      }

      @keyframes morphing-slow {
        0%, 100% {
          border-radius: 60% 40% 50% 30%;
          transform: translateX(0px) translateY(0px) rotate(0deg);
        }
        33% {
          border-radius: 40% 50% 30% 60%;
          transform: translateX(15px) translateY(-20px) rotate(120deg);
        }
        66% {
          border-radius: 50% 30% 60% 40%;
          transform: translateX(-15px) translateY(-10px) rotate(240deg);
        }
      }

      /* Dark Mode Styles */
      .dark {
        color-scheme: dark;
      }

      .dark body {
        background-color: #0f172a;
        color: #e2e8f0;
      }

      .dark nav {
        background-color: rgba(15, 23, 42, 0.95);
        backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(51, 65, 85, 0.3);
      }

      .dark .bg-white {
        background-color: #1e293b;
        border: 1px solid rgba(51, 65, 85, 0.3);
      }

      .dark .bg-gray-50 {
        background-color: #0f172a;
      }

      .dark .text-gray-800 {
        color: #e2e8f0;
      }

      .dark .text-gray-600 {
        color: #94a3b8;
      }

      .dark .text-gray-700 {
        color: #cbd5e1;
      }

      .dark .text-gray-400 {
        color: #64748b;
      }

      .dark .border-gray-300 {
        border-color: #334155;
      }

      .dark .border-gray-700 {
        border-color: #475569;
      }

      .dark .bg-gray-100 {
        background-color: #334155;
      }

      .dark .bg-gray-200 {
        background-color: #475569;
      }

      .dark .bg-blue-100 {
        background-color: rgba(59, 130, 246, 0.1);
      }

      .dark .shadow-md {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
      }

      .dark .shadow-xl {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
      }

      .dark #hero {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%) !important;
      }

      .dark footer {
        background: linear-gradient(to right, #020617, #0f172a, #020617);
      }

      /* Dark Mode Toggle Button */
      .theme-toggle {
        position: relative;
        width: 50px;
        height: 26px;
        background-color: #374151;
        border-radius: 50px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        border: 2px solid #6b7280;
      }

      .dark .theme-toggle {
        background-color: #15B8A6;
        border-color: #15B8A6;
      }

      .theme-toggle::before {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 18px;
        height: 18px;
        background-color: white;
        border-radius: 50%;
        transition: transform 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .dark .theme-toggle::before {
        transform: translateX(24px);
      }

      .theme-toggle-icon {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        font-size: 12px;
        transition: opacity 0.3s ease;
      }

      .theme-toggle .sun-icon {
        left: 6px;
        opacity: 1;
        color: #fbbf24;
      }

      .theme-toggle .moon-icon {
        right: 6px;
        opacity: 0;
        color: #e2e8f0;
      }

      .dark .theme-toggle .sun-icon {
        opacity: 0;
      }

      .dark .theme-toggle .moon-icon {
        opacity: 1;
      }

      /* Smooth transitions for theme switching */
      * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
      }

      /* Enhanced Contact Form Styles */
      .form-group {
        position: relative;
      }

      .form-input {
        transition: all 0.3s ease;
      }

      .form-input:focus {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(21, 184, 166, 0.15);
      }

      .form-input.error {
        border-color: #ef4444;
        background-color: rgba(239, 68, 68, 0.05);
      }

      .form-input.success {
        border-color: #10b981;
        background-color: rgba(16, 185, 129, 0.05);
      }

      .error-message {
        color: #ef4444;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
      }

      .error-message.show {
        opacity: 1;
        transform: translateY(0);
      }

      .success-message {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.5s ease;
      }

      .success-message.show {
        opacity: 1;
        transform: translateY(0);
      }

      .error-notification {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.5s ease;
      }

      .error-notification.show {
        opacity: 1;
        transform: translateY(0);
      }

      .character-counter {
        font-size: 0.75rem;
        color: #6b7280;
        text-align: right;
        margin-top: 0.25rem;
        transition: color 0.3s ease;
      }

      .character-counter.warning {
        color: #f59e0b;
      }

      .character-counter.error {
        color: #ef4444;
      }

      .submit-button {
        position: relative;
        overflow: hidden;
      }

      .submit-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      .submit-button .loading-spinner {
        display: none;
        width: 20px;
        height: 20px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      .submit-button.loading .loading-spinner {
        display: inline-block;
      }

      .submit-button.loading .button-text {
        display: none;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* Dark mode form styles */
      .dark .form-input.error {
        background-color: rgba(239, 68, 68, 0.1);
      }

      .dark .form-input.success {
        background-color: rgba(16, 185, 129, 0.1);
      }

      .dark .character-counter {
        color: #9ca3af;
      }

      /* Swiper Customization */
      .projects-swiper {
        padding: 20px 0 60px 0;
      }

      .projects-swiper .swiper-slide {
        height: auto;
        display: flex;
      }

      .projects-swiper .swiper-pagination {
        bottom: 20px;
      }

      .projects-swiper .swiper-pagination-bullet {
        background: #15B8A6;
        opacity: 0.3;
        width: 12px;
        height: 12px;
      }

      .projects-swiper .swiper-pagination-bullet-active {
        opacity: 1;
        transform: scale(1.2);
      }

      .projects-swiper .swiper-button-next,
      .projects-swiper .swiper-button-prev {
        color: #15B8A6;
        background: rgba(255, 255, 255, 0.9);
        width: 44px;
        height: 44px;
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
      }

      .projects-swiper .swiper-button-next:hover,
      .projects-swiper .swiper-button-prev:hover {
        background: rgba(255, 255, 255, 1);
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }

      .projects-swiper .swiper-button-next::after,
      .projects-swiper .swiper-button-prev::after {
        font-size: 18px;
        font-weight: bold;
      }

      /* Dark mode swiper styles */
      .dark .projects-swiper .swiper-button-next,
      .dark .projects-swiper .swiper-button-prev {
        background: rgba(30, 41, 59, 0.9);
        color: #15B8A6;
      }

      .dark .projects-swiper .swiper-button-next:hover,
      .dark .projects-swiper .swiper-button-prev:hover {
        background: rgba(30, 41, 59, 1);
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .projects-swiper .swiper-button-next,
        .projects-swiper .swiper-button-prev {
          display: none;
        }
      }

      /* IA Logo Styles - Tech Design */
      .logo-container {
        text-decoration: none;
        transition: all 0.3s ease;
        position: relative;
      }

      .logo-container:hover {
        transform: scale(1.05);
      }

      .logo-ia {
        position: relative;
        display: flex;
        align-items: center;
        font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
        font-size: 2rem;
        font-weight: 900;
        letter-spacing: 2px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #15B8A6, #10b981, #06b6d4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-transform: uppercase;
        position: relative;
      }

      /* Tech border effect */
      .logo-ia::before {
        content: '';
        position: absolute;
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
        background: linear-gradient(45deg, #15B8A6, #10b981, #06b6d4, #8b5cf6);
        border-radius: 8px;
        opacity: 0;
        transition: all 0.3s ease;
        z-index: -1;
        animation: techGlow 3s ease-in-out infinite;
      }

      .logo-container:hover .logo-ia::before {
        opacity: 0.3;
      }

      /* Circuit pattern overlay */
      .logo-ia::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
          linear-gradient(90deg, transparent 48%, rgba(21, 184, 166, 0.1) 49%, rgba(21, 184, 166, 0.1) 51%, transparent 52%),
          linear-gradient(0deg, transparent 48%, rgba(16, 185, 129, 0.1) 49%, rgba(16, 185, 129, 0.1) 51%, transparent 52%);
        background-size: 20px 20px;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      }

      .logo-container:hover .logo-ia::after {
        opacity: 1;
      }

      .logo-i {
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
        text-shadow: 0 0 10px rgba(21, 184, 166, 0.5);
      }

      .logo-a {
        position: relative;
        z-index: 1;
        transition: all 0.3s ease;
        text-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
      }

      /* Hover effects - Tech style */
      .logo-container:hover .logo-i {
        transform: translateX(-2px);
        text-shadow:
          0 0 5px rgba(21, 184, 166, 0.8),
          0 0 10px rgba(21, 184, 166, 0.6),
          0 0 15px rgba(21, 184, 166, 0.4);
      }

      .logo-container:hover .logo-a {
        transform: translateX(2px);
        text-shadow:
          0 0 5px rgba(16, 185, 129, 0.8),
          0 0 10px rgba(16, 185, 129, 0.6),
          0 0 15px rgba(16, 185, 129, 0.4);
      }

      /* Tech glow animation */
      @keyframes techGlow {
        0%, 100% {
          background: linear-gradient(45deg, #15B8A6, #10b981, #06b6d4, #8b5cf6);
        }
        25% {
          background: linear-gradient(45deg, #10b981, #06b6d4, #8b5cf6, #15B8A6);
        }
        50% {
          background: linear-gradient(45deg, #06b6d4, #8b5cf6, #15B8A6, #10b981);
        }
        75% {
          background: linear-gradient(45deg, #8b5cf6, #15B8A6, #10b981, #06b6d4);
        }
      }

      /* Digital matrix effect */
      .logo-container::before {
        content: '';
        position: absolute;
        top: -20px;
        left: -20px;
        right: -20px;
        bottom: -20px;
        background:
          radial-gradient(circle at 20% 20%, rgba(21, 184, 166, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 40% 60%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: -2;
        border-radius: 12px;
      }

      .logo-container:hover::before {
        opacity: 1;
      }

      /* Footer logo styles - Tech Design */
      .footer-logo {
        background: linear-gradient(135deg, #ffffff, #e2e8f0, #cbd5e1);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .footer-logo .logo-i,
      .footer-logo .logo-a {
        text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
      }

      .footer-logo:hover .logo-i {
        text-shadow:
          0 0 5px rgba(21, 184, 166, 0.8),
          0 0 10px rgba(21, 184, 166, 0.6),
          0 0 15px rgba(21, 184, 166, 0.4);
      }

      .footer-logo:hover .logo-a {
        text-shadow:
          0 0 5px rgba(16, 185, 129, 0.8),
          0 0 10px rgba(16, 185, 129, 0.6),
          0 0 15px rgba(16, 185, 129, 0.4);
      }

      .footer-logo:hover {
        background: linear-gradient(135deg, #15B8A6, #10b981, #06b6d4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      /* Dark mode logo styles - Enhanced Tech */
      .dark .logo-ia {
        background: linear-gradient(135deg, #5eead4, #6ee7b7, #7dd3fc, #c4b5fd);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .dark .logo-i {
        text-shadow: 0 0 15px rgba(94, 234, 212, 0.6);
      }

      .dark .logo-a {
        text-shadow: 0 0 15px rgba(110, 231, 183, 0.6);
      }

      .dark .logo-container:hover .logo-i {
        text-shadow:
          0 0 5px rgba(94, 234, 212, 1),
          0 0 10px rgba(94, 234, 212, 0.8),
          0 0 20px rgba(94, 234, 212, 0.6),
          0 0 30px rgba(94, 234, 212, 0.4);
      }

      .dark .logo-container:hover .logo-a {
        text-shadow:
          0 0 5px rgba(110, 231, 183, 1),
          0 0 10px rgba(110, 231, 183, 0.8),
          0 0 20px rgba(110, 231, 183, 0.6),
          0 0 30px rgba(110, 231, 183, 0.4);
      }

      .dark .logo-ia::before {
        background: linear-gradient(45deg, #5eead4, #6ee7b7, #7dd3fc, #c4b5fd);
        animation: darkTechGlow 3s ease-in-out infinite;
      }

      @keyframes darkTechGlow {
        0%, 100% {
          background: linear-gradient(45deg, #5eead4, #6ee7b7, #7dd3fc, #c4b5fd);
        }
        25% {
          background: linear-gradient(45deg, #6ee7b7, #7dd3fc, #c4b5fd, #5eead4);
        }
        50% {
          background: linear-gradient(45deg, #7dd3fc, #c4b5fd, #5eead4, #6ee7b7);
        }
        75% {
          background: linear-gradient(45deg, #c4b5fd, #5eead4, #6ee7b7, #7dd3fc);
        }
      }

      /* Tech dots indicator */
      .tech-dots {
        position: absolute;
        top: -8px;
        right: -12px;
        display: flex;
        gap: 2px;
        opacity: 0.7;
        transition: all 0.3s ease;
      }

      .tech-dots .dot {
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: linear-gradient(45deg, #15B8A6, #10b981);
        animation: techPulse 2s ease-in-out infinite;
      }

      .tech-dots .dot:nth-child(1) {
        animation-delay: 0s;
      }

      .tech-dots .dot:nth-child(2) {
        animation-delay: 0.3s;
      }

      .tech-dots .dot:nth-child(3) {
        animation-delay: 0.6s;
      }

      .logo-container:hover .tech-dots {
        opacity: 1;
        transform: scale(1.2);
      }

      .logo-container:hover .tech-dots .dot {
        background: linear-gradient(45deg, #06b6d4, #8b5cf6);
        box-shadow: 0 0 8px rgba(6, 182, 212, 0.6);
      }

      @keyframes techPulse {
        0%, 100% {
          opacity: 0.3;
          transform: scale(1);
        }
        50% {
          opacity: 1;
          transform: scale(1.3);
        }
      }

      /* Dark mode tech dots */
      .dark .tech-dots .dot {
        background: linear-gradient(45deg, #5eead4, #6ee7b7);
      }

      .dark .logo-container:hover .tech-dots .dot {
        background: linear-gradient(45deg, #7dd3fc, #c4b5fd);
        box-shadow: 0 0 8px rgba(125, 211, 252, 0.8);
      }

      /* Binary code effect (optional enhancement) */
      .logo-ia:hover::before {
        content: '01001001 01000001';
        position: absolute;
        top: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-family: 'Courier New', monospace;
        font-size: 8px;
        color: rgba(21, 184, 166, 0.4);
        opacity: 0;
        animation: binaryFade 2s ease-in-out;
        pointer-events: none;
        white-space: nowrap;
      }

      @keyframes binaryFade {
        0% {
          opacity: 0;
          transform: translateX(-50%) translateY(10px);
        }
        50% {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
        100% {
          opacity: 0;
          transform: translateX(-50%) translateY(-10px);
        }
      }

      /* Navigation Logo Styles */
      .nav-logo-container {
        text-decoration: none;
        transition: all 0.3s ease;
        height: 40px;
        align-items: center;
      }

      .nav-logo-container:hover {
        transform: scale(1.02);
      }

      /* Logo Image Wrapper */
      .logo-image-wrapper {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        overflow: hidden;
        background: linear-gradient(135deg, #15B8A6, #10b981);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
      }

      .nav-logo-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
      }

      .nav-logo-container:hover .logo-image-wrapper {
        box-shadow: 0 4px 12px rgba(21, 184, 166, 0.3);
        transform: rotate(5deg);
      }

      .nav-logo-container:hover .nav-logo-img {
        transform: scale(1.1);
      }

      /* Navigation specific logo sizing */
      .nav-logo {
        font-size: 1.5rem !important;
        letter-spacing: 1px !important;
        height: 32px;
        align-items: center;
      }

      .nav-logo .tech-dots {
        top: -4px;
        right: -8px;
      }

      .nav-logo .tech-dots .dot {
        width: 3px;
        height: 3px;
      }

      /* Brand text */
      .nav-brand-text {
        font-family: 'Pacifico', cursive;
        font-size: 1.25rem;
        color: #15B8A6;
        transition: all 0.3s ease;
        white-space: nowrap;
      }

      .nav-logo-container:hover .nav-brand-text {
        color: #0d9488;
        text-shadow: 0 2px 4px rgba(21, 184, 166, 0.3);
      }

      /* Dark mode navigation logo */
      .dark .nav-brand-text {
        color: #5eead4;
      }

      .dark .nav-logo-container:hover .nav-brand-text {
        color: #7dd3fc;
        text-shadow: 0 2px 4px rgba(94, 234, 212, 0.4);
      }

      .dark .logo-image-wrapper {
        background: linear-gradient(135deg, #5eead4, #6ee7b7);
      }

      .dark .nav-logo-container:hover .logo-image-wrapper {
        box-shadow: 0 4px 12px rgba(94, 234, 212, 0.4);
      }

      /* Responsive navigation logo */
      @media (max-width: 768px) {
        .nav-logo-container {
          height: 36px;
        }

        .logo-image-wrapper {
          width: 28px;
          height: 28px;
        }

        .nav-logo {
          font-size: 1.25rem !important;
          letter-spacing: 0px !important;
          height: 28px;
        }

        .nav-brand-text {
          font-size: 1.1rem;
        }

        .nav-logo .tech-dots {
          top: -3px;
          right: -6px;
        }

        .nav-logo .tech-dots .dot {
          width: 2px;
          height: 2px;
        }
      }

      @media (max-width: 480px) {
        .nav-logo-container {
          height: 32px;
          gap: 8px;
        }

        .logo-image-wrapper {
          width: 24px;
          height: 24px;
        }

        .nav-logo {
          font-size: 1rem !important;
          height: 24px;
        }

        .nav-brand-text {
          font-size: 1rem;
        }

        .nav-logo .tech-dots {
          top: -2px;
          right: -4px;
        }

        .nav-logo .tech-dots .dot {
          width: 1.5px;
          height: 1.5px;
        }
      }

      /* Hide brand text on very small screens */
      @media (max-width: 360px) {
        .nav-brand-text {
          display: none;
        }
      }

      /* General logo styles for other instances */
      .logo-ia:not(.nav-logo) {
        font-size: 2rem;
        letter-spacing: 2px;
      }

      /* Responsive logo */
      @media (max-width: 768px) {
        .logo-ia:not(.nav-logo) {
          font-size: 1.75rem;
          letter-spacing: 1px;
        }

        .tech-dots:not(.nav-logo .tech-dots) {
          top: -6px;
          right: -8px;
        }

        .tech-dots:not(.nav-logo .tech-dots) .dot {
          width: 3px;
          height: 3px;
        }
      }

      @media (max-width: 480px) {
        .logo-ia:not(.nav-logo) {
          font-size: 1.5rem;
          letter-spacing: 0px;
        }

        .tech-dots:not(.nav-logo .tech-dots) {
          top: -4px;
          right: -6px;
        }

        .tech-dots:not(.nav-logo .tech-dots) .dot {
          width: 2px;
          height: 2px;
        }
      }